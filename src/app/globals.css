@import "tailwindcss";

@plugin "tailwindcss-animate";

@custom-variant dark (&:is(.dark *));

@theme inline {
  --breakpoint-xs: 30rem;
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
  --color-sidebar-ring: var(--sidebar-ring);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar: var(--sidebar);
  --color-chart-5: var(--chart-5);
  --color-chart-4: var(--chart-4);
  --color-chart-3: var(--chart-3);
  --color-chart-2: var(--chart-2);
  --color-chart-1: var(--chart-1);
  --color-ring: var(--ring);
  --color-input: var(--input);
  --color-border: var(--border);
  --color-destructive: var(--destructive);
  --color-accent-foreground: var(--accent-foreground);
  --color-accent: var(--accent);
  --color-muted-foreground: var(--muted-foreground);
  --color-muted: var(--muted);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-secondary: var(--secondary);
  --color-primary-foreground: var(--primary-foreground);
  --color-primary: var(--primary);
  --color-popover-foreground: var(--popover-foreground);
  --color-popover: var(--popover);
  --color-card-foreground: var(--card-foreground);
  --color-card: var(--card);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
  --ease-smooth: cubic-bezier(0.33, 1, 0.68, 1);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
  --spacing-relaxed: 1.65;
}

:root {
  --background: hsl(0 0% 100%);
  --foreground: hsl(60 3% 6%);
  --card: hsl(0 0% 100%);
  --card-foreground: hsl(60 3% 6%);
  --popover: hsl(0 0% 100%);
  --popover-foreground: hsl(60 3% 6%);
  --primary: hsl(220.9 39.3% 11%);
  --primary-foreground: hsl(210 20% 98%);
  --secondary: hsl(220 14.3% 95%);
  --secondary-foreground: hsl(0 0% 50%);
  --muted: hsl(220 14.3% 95.9%);
  --muted-foreground: hsl(220 8.9% 46.1%);
  --accent: hsl(0 0% 90%);
  --accent-foreground: hsl(220.9 39.3% 11%);
  --destructive: oklch(0.577 0.245 27.325);
  --destructive-foreground: hsl(210 20% 98%);
  --border: hsl(220 13% 91%);
  --input: hsl(220 13% 91%);
  --ring: hsl(60 3% 6%);
  --chart-1: oklch(0.646 0.222 41.116);
  --chart-2: oklch(0.6 0.118 184.704);
  --chart-3: oklch(0.398 0.07 227.392);
  --chart-4: oklch(0.828 0.189 84.429);
  --chart-5: oklch(0.769 0.188 70.08);
  --sidebar: oklch(0.985 0 0);
  --sidebar-foreground: oklch(0.145 0 0);
  --sidebar-primary: oklch(0.205 0 0);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.97 0 0);
  --sidebar-accent-foreground: oklch(0.205 0 0);
  --sidebar-border: oklch(0.922 0 0);
  --sidebar-ring: oklch(0.708 0 0);
  --radius: 0.5rem;

  --sh-class: #7aa2f7;
  --sh-sign: #89ddff;
  --sh-string: #9ece6a;
  --sh-keyword: #bb9af7;
  --sh-comment: #565f89;
  --sh-jsxliterals: #7aa2f7;
  --sh-property: #73daca;
  --sh-entity: #e0af68;
}

.dark {
  --background: hsl(0 0% 12%);
  --foreground: hsl(0 0% 90%);
  --card: hsl(60 3% 6%);
  --card-foreground: hsl(0 0% 90%);
  --popover: hsl(60 3% 6%);
  --popover-foreground: hsl(0 0% 90%);
  --primary: hsl(0 0% 90%);
  --primary-foreground: hsl(60 3% 6%);
  --secondary: hsl(0 0% 22%);
  --secondary-foreground: hsl(0 0% 53%);
  --muted: hsl(0 0% 15%);
  --muted-foreground: hsl(0 0% 64%);
  --accent: hsl(0 0% 15%);
  --accent-foreground: hsl(0 0% 90%);
  --destructive: oklch(0.704 0.191 22.216);
  --destructive-foreground: hsl(210 20% 98%);
  --border: hsl(0 0% 15%);
  --input: hsl(0 0% 15%);
  --ring: hsl(0 0% 90%);
  --chart-1: oklch(0.488 0.243 264.376);
  --chart-2: oklch(0.696 0.17 162.48);
  --chart-3: oklch(0.769 0.188 70.08);
  --chart-4: oklch(0.627 0.265 303.9);
  --chart-5: oklch(0.645 0.246 16.439);
  --sidebar: oklch(0.205 0 0);
  --sidebar-foreground: oklch(0.985 0 0);
  --sidebar-primary: oklch(0.488 0.243 264.376);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.269 0 0);
  --sidebar-accent-foreground: oklch(0.985 0 0);
  --sidebar-border: oklch(1 0 0 / 10%);
  --sidebar-ring: oklch(0.556 0 0);

  --sh-class: #7aa2f7;
  --sh-sign: #89ddff;
  --sh-string: #9ece6a;
  --sh-keyword: #bb9af7;
  --sh-comment: #565f89;
  --sh-jsxliterals: #7aa2f7;
  --sh-property: #73daca;
  --sh-entity: #e0af68;
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  html {
    @apply bg-background text-foreground;
  }
}

@layer utilities {
  .transition-smooth {
    @apply ease-smooth transition duration-500;
  }
}