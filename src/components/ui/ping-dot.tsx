import type { VariantProps } from 'class-variance-authority';

import { cva } from 'class-variance-authority';
import React from 'react';

import { cn } from '@/utils';

const pingDotVariants = cva('', {
  variants: {
    color: {
      green: 'green',
      red: 'red',
    },
    size: {
      sm: 'size-2',
      md: 'size-3',
      lg: 'size-4',
    },
  },
  defaultVariants: {
    color: 'green',
    size: 'sm',
  },
});

const PingDot = React.forwardRef<
  HTMLSpanElement,
  React.HTMLAttributes<HTMLSpanElement> & VariantProps<typeof pingDotVariants>
>(({ className, color = 'green', size, ...props }, ref) => (
  <span
    ref={ref}
    className={cn(
      'relative inline-flex rounded-full',
      `before:absolute before:inset-0 before:animate-ping before:rounded-full before:bg-${color}-400 before:border before:border-${color}-600`,
      `bg-${color}-500`,
      pingDotVariants({ size }),
      className
    )}
    {...props}
  />
));

PingDot.displayName = 'PingDot';

export { PingDot, pingDotVariants };
