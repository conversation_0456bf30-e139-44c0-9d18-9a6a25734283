import { SiteConfig } from '@/configuration';
import type { Metadata } from 'next';

const twitterHandle = SiteConfig.site.twitterHandle;
const siteUrl = new URL(SiteConfig.site.url);
const siteName = SiteConfig.site.name;
const siteDescription = 'Software Engineer, freelancer & quick learner.';
const siteAuthor = '<PERSON>';
const keywords = [
  'Tehseen',
  'Web Design',
  'Product Design',
  'Web Development',
  'SaaS Design',
  'Web Design',
  'SaaS Development',
  'MVP Development',
  'Service-based Businesses',
  'Product Development',
  'UX/UI Design',
  'Digital Design',
  'User Interface Design',
  'SaaS Product Design',
  'Service Business Solutions',
  'Web Development Agency',
  'Custom Web Design',
  'Creative Web Studio',
  'Design Studio',
  'Software Consulting',
  'Software Development',
  'Web Design Agency',
  'Web Development Agency',
  'Web Design Company',
  'Web Design Studio',
];

export const createMetadata = (overrides: Partial<Metadata>): Metadata => {
  return {
    metadataBase: siteUrl,
    title: {
      default: siteName,
      template: `%s | ${siteName}`,
    },
    description: siteDescription,
    keywords,
    openGraph: {
      title: siteName,
      description: siteDescription,
      siteName: siteName,
      url: siteUrl.href,
      locale: 'en_US',
      type: 'website',
      images: '/og/opengraph-image.png',
    },
    twitter: {
      title: siteName,
      description: siteDescription,
      images: '/og/twitter-image.png',
      card: 'summary_large_image',
      creator: twitterHandle,
      site: siteUrl.href,
    },
    robots: {
      index: true,
      follow: true,
      nocache: true,
      'max-image-preview': 'large',
      googleBot: {
        index: true,
        follow: true,
        noimageindex: true,
        'max-video-preview': -1,
        'max-image-preview': 'large',
        'max-snippet': -1,
      },
    },
    // Extra metadata
    applicationName: siteName,
    authors: [{ name: siteAuthor, url: siteUrl.href }],
    creator: siteAuthor,
    appleWebApp: {
      capable: true,
      statusBarStyle: 'default',
      title: siteName,
    },
    ...overrides,
  };
};
